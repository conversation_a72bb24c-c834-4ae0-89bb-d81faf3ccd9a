<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Shop Management';
$currentPage = 'shops';

// Handle shop actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'verify_shop':
                $shopId = intval($_POST['shop_id']);
                $stmt = $pdo->prepare("UPDATE laundry_shops SET is_verified = 1 WHERE id = ?");
                if ($stmt->execute([$shopId])) {
                    $success = "Shop verified successfully!";
                } else {
                    $error = "Failed to verify shop.";
                }
                break;

            case 'deactivate_shop':
                $shopId = intval($_POST['shop_id']);
                $stmt = $pdo->prepare("UPDATE laundry_shops SET is_active = 0 WHERE id = ?");
                if ($stmt->execute([$shopId])) {
                    $success = "Shop deactivated successfully!";
                } else {
                    $error = "Failed to deactivate shop.";
                }
                break;

            case 'update_commission':
                $shopId = intval($_POST['shop_id']);
                $commission = floatval($_POST['commission_percentage']);
                $stmt = $pdo->prepare("UPDATE laundry_shops SET commission_percentage = ? WHERE id = ?");
                if ($stmt->execute([$commission, $shopId])) {
                    $success = "Commission rate updated successfully!";
                } else {
                    $error = "Failed to update commission rate.";
                }
                break;

            case 'create_shop_owner':
                $shopId = intval($_POST['shop_id']);
                $username = sanitize($_POST['username']);
                $password = $_POST['password'];

                // Get shop details
                $shopStmt = $pdo->prepare("SELECT * FROM laundry_shops WHERE id = ?");
                $shopStmt->execute([$shopId]);
                $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);

                if ($shop) {
                    require_once '../includes/ShopOwnerManager.php';
                    $shopOwnerManager = new ShopOwnerManager($pdo);

                    // Check if shop owner already exists
                    if (!$shopOwnerManager->shopOwnerExistsByShopId($shopId)) {
                        $shopOwnerId = $shopOwnerManager->createShopOwner(
                            $shopId,
                            $username,
                            $password,
                            $shop['email'],
                            $shop['phone'],
                            $shop['owner_name']
                        );

                        if ($shopOwnerId) {
                            // Verify the shop owner account immediately
                            $shopOwnerManager->verifyShopOwner($shopOwnerId);
                            $success = "Shop owner account created successfully! Username: " . $username;
                        } else {
                            $error = "Failed to create shop owner account. Username may already exist.";
                        }
                    } else {
                        $error = "Shop owner account already exists for this shop.";
                    }
                } else {
                    $error = "Shop not found.";
                }
                break;
        }
    }
}

// Get shops with pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';

// Build query
$whereClause = "WHERE 1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (ls.name LIKE ? OR ls.owner_name LIKE ? OR ls.phone LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($status !== 'all') {
    switch ($status) {
        case 'active':
            $whereClause .= " AND ls.is_active = 1";
            break;
        case 'inactive':
            $whereClause .= " AND ls.is_active = 0";
            break;
        case 'verified':
            $whereClause .= " AND ls.is_verified = 1";
            break;
        case 'unverified':
            $whereClause .= " AND ls.is_verified = 0";
            break;
    }
}

// Get total count
$countSql = "SELECT COUNT(*) FROM laundry_shops ls $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalShops = $countStmt->fetchColumn();
$totalPages = ceil($totalShops / $limit);

// Get shops with shop owner info
$sql = "
    SELECT
        ls.*,
        d.name as division_name,
        dist.name as district_name,
        up.name as upazilla_name,
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.total), 0) as total_revenue,
        so.id as shop_owner_id,
        so.username as shop_owner_username,
        so.is_verified as shop_owner_verified
    FROM laundry_shops ls
    LEFT JOIN divisions d ON ls.division_id = d.id
    LEFT JOIN districts dist ON ls.district_id = dist.id
    LEFT JOIN upazillas up ON ls.upazilla_id = up.id
    LEFT JOIN orders o ON ls.id = o.shop_id AND o.status = 'delivered'
    LEFT JOIN shop_owners so ON ls.id = so.shop_id
    $whereClause
    GROUP BY ls.id
    ORDER BY ls.created_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Shop Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShopModal">
            <i class="fas fa-plus"></i> Add New Shop
        </button>
    </div>
</div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($success) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?= htmlspecialchars($search) ?>" placeholder="Shop name, owner, phone...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Shops</option>
                                <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                <option value="verified" <?= $status === 'verified' ? 'selected' : '' ?>>Verified</option>
                                <option value="unverified" <?= $status === 'unverified' ? 'selected' : '' ?>>Unverified</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="shops.php" class="btn btn-outline-secondary">Reset</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Shops Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Shops (<?= $totalShops ?> total)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($shops)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-store fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No shops found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Shop</th>
                                        <th>Owner</th>
                                        <th>Location</th>
                                        <th>Rating</th>
                                        <th>Commission</th>
                                        <th>Orders</th>
                                        <th>Revenue</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($shops as $shop): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($shop['profile_image_url']): ?>
                                                        <img src="<?= htmlspecialchars($shop['profile_image_url']) ?>"
                                                             class="rounded-circle me-2" width="40" height="40">
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center"
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-store text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-bold"><?= htmlspecialchars($shop['name']) ?></div>
                                                        <small class="text-muted"><?= htmlspecialchars($shop['phone']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($shop['owner_name']) ?></td>
                                            <td>
                                                <small>
                                                    <?= htmlspecialchars($shop['upazilla_name'] ?? '') ?>,
                                                    <?= htmlspecialchars($shop['district_name'] ?? '') ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-warning text-dark me-1">
                                                        <i class="fas fa-star"></i> <?= number_format($shop['rating'], 1) ?>
                                                    </span>
                                                    <small class="text-muted">(<?= $shop['total_reviews'] ?>)</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($shop['commission_percentage'], 1) ?>%</span>
                                            </td>
                                            <td><?= number_format($shop['total_orders']) ?></td>
                                            <td>৳<?= number_format($shop['total_revenue'], 2) ?></td>
                                            <td>
                                                <?php if ($shop['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>

                                                <?php if ($shop['is_verified']): ?>
                                                    <span class="badge bg-primary">Verified</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Unverified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                                            data-bs-toggle="dropdown">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="shop_details.php?id=<?= $shop['id'] ?>">
                                                            <i class="fas fa-eye"></i> View Details
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="edit_shop.php?id=<?= $shop['id'] ?>">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a></li>
                                                        <?php if (!$shop['is_verified']): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="action" value="verify_shop">
                                                                    <input type="hidden" name="shop_id" value="<?= $shop['id'] ?>">
                                                                    <button type="submit" class="dropdown-item text-success">
                                                                        <i class="fas fa-check"></i> Verify Shop
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button type="button" class="dropdown-item text-info"
                                                                    onclick="updateCommission(<?= $shop['id'] ?>, <?= $shop['commission_percentage'] ?>)">
                                                                <i class="fas fa-percentage"></i> Update Commission
                                                            </button>
                                                        </li>
                                                        <?php if ($shop['is_verified'] && !$shop['shop_owner_id']): ?>
                                                            <li>
                                                                <button type="button" class="dropdown-item text-success"
                                                                        onclick="createShopOwner(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                                    <i class="fas fa-user-plus"></i> Create Shop Owner Account
                                                                </button>
                                                            </li>
                                                        <?php elseif ($shop['shop_owner_id']): ?>
                                                            <li>
                                                                <span class="dropdown-item text-muted">
                                                                    <i class="fas fa-user-check"></i> Owner: <?= htmlspecialchars($shop['shop_owner_username']) ?>
                                                                </span>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($shop['is_active']): ?>
                                                            <li>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="action" value="deactivate_shop">
                                                                    <input type="hidden" name="shop_id" value="<?= $shop['id'] ?>">
                                                                    <button type="submit" class="dropdown-item text-danger"
                                                                            onclick="return confirm('Are you sure you want to deactivate this shop?')">
                                                                        <i class="fas fa-ban"></i> Deactivate
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Shops pagination">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

<!-- Add Shop Modal -->
<div class="modal fade" id="addShopModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Shop</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="add_shop.php">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shop_name" class="form-label">Shop Name *</label>
                                <input type="text" class="form-control" id="shop_name" name="shop_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="owner_name" class="form-label">Owner Name *</label>
                                <input type="text" class="form-control" id="owner_name" name="owner_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address *</label>
                        <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                    </div>
                    <!-- Location Picker Section -->
                    <div class="mb-3">
                        <label class="form-label">Shop Location *</label>
                        <div class="card">
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="location_search"
                                               placeholder="Search for location or enter address...">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="searchLocation()">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                                <div id="map" style="height: 300px; border-radius: 8px;"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-12">
                                            <small class="text-muted d-block mb-2">
                                                <i class="fas fa-info-circle"></i> Click on the map to set shop location or use quick locations:
                                            </small>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(23.8103, 90.4125, 'Dhaka')">
                                                    Dhaka
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(22.3569, 91.7832, 'Chittagong')">
                                                    Chittagong
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(24.3636, 88.6241, 'Rajshahi')">
                                                    Rajshahi
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(24.8949, 91.8687, 'Sylhet')">
                                                    Sylhet
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(25.7439, 89.2752, 'Rangpur')">
                                                    Rangpur
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="getCurrentLocation()">
                                                    <i class="fas fa-location-arrow"></i> My Location
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude *</label>
                                <input type="number" class="form-control" id="latitude" name="latitude"
                                       step="any" min="-90" max="90" required onchange="updateMapFromCoordinates()">
                                <div class="form-text">GPS coordinates for map location</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude *</label>
                                <input type="number" class="form-control" id="longitude" name="longitude"
                                       step="any" min="-180" max="180" required onchange="updateMapFromCoordinates()">
                                <div class="form-text">GPS coordinates for map location</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                <input type="number" class="form-control" id="commission_rate" name="commission_rate"
                                       min="0" max="50" step="0.1" value="15">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="is_verified" class="form-label">Verification Status</label>
                                <select class="form-select" id="is_verified" name="is_verified">
                                    <option value="0">Unverified</option>
                                    <option value="1">Verified</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Shop</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Commission Update Modal -->
<div class="modal fade" id="commissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Commission Rate</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_commission">
                    <input type="hidden" name="shop_id" id="commission_shop_id">
                    <div class="mb-3">
                        <label for="commission_percentage" class="form-label">Commission Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="commission_percentage"
                                   name="commission_percentage" min="0" max="50" step="0.1" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="form-text">Enter commission percentage (0-50%)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Commission</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Shop Owner Modal -->
<div class="modal fade" id="createShopOwnerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Shop Owner Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_shop_owner">
                    <input type="hidden" name="shop_id" id="shop_owner_shop_id">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Creating account for shop: <strong id="shop_owner_shop_name"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="shop_owner_username"
                               name="username" required>
                        <div class="form-text">Shop owner will use this to login</div>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_password" class="form-label">Password *</label>
                        <input type="password" class="form-control" id="shop_owner_password"
                               name="password" required minlength="8">
                        <div class="form-text">Minimum 8 characters</div>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_password_confirm" class="form-label">Confirm Password *</label>
                        <input type="password" class="form-control" id="shop_owner_password_confirm" required>
                        <div class="invalid-feedback">Passwords do not match</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="createShopOwnerBtn">Create Account</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OpenStreetMap with Leaflet (Free Alternative) -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let marker;

// Initialize map when modal is shown
document.getElementById('addShopModal').addEventListener('shown.bs.modal', function () {
    setTimeout(initializeMap, 100); // Small delay to ensure modal is fully shown
});

function initializeMap() {
    // Check if map is already initialized
    if (map) {
        map.invalidateSize();
        return;
    }

    // Default location (Dhaka, Bangladesh)
    map = L.map('map').setView([23.8103, 90.4125], 13);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Add click listener to map
    map.on('click', function(e) {
        setMapLocation(e.latlng.lat, e.latlng.lng);
    });

    // Try to get user's current location
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLocation = [position.coords.latitude, position.coords.longitude];
            map.setView(userLocation, 15);
        });
    }
}

function setMapLocation(lat, lng) {
    // Remove existing marker
    if (marker) {
        map.removeLayer(marker);
    }

    // Add new marker
    marker = L.marker([lat, lng], {
        draggable: true
    }).addTo(map);

    // Update coordinate inputs
    document.getElementById('latitude').value = lat.toFixed(8);
    document.getElementById('longitude').value = lng.toFixed(8);

    // Center map on location
    map.setView([lat, lng], map.getZoom());

    // Add drag listener to marker
    marker.on('dragend', function(e) {
        const position = e.target.getLatLng();
        document.getElementById('latitude').value = position.lat.toFixed(8);
        document.getElementById('longitude').value = position.lng.toFixed(8);
        reverseGeocode(position.lat, position.lng);
    });

    // Reverse geocode to get address
    reverseGeocode(lat, lng);
}

function reverseGeocode(lat, lng) {
    // Using Nominatim API for reverse geocoding
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`)
        .then(response => response.json())
        .then(data => {
            if (data.display_name) {
                document.getElementById('address').value = data.display_name;
            }
        })
        .catch(error => {
            console.error('Reverse geocoding failed:', error);
        });
}

function searchLocation() {
    const searchText = document.getElementById('location_search').value;
    if (!searchText) return;

    // Using Nominatim API for geocoding
    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchText)}&countrycodes=bd&limit=1`)
        .then(response => response.json())
        .then(data => {
            if (data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);

                map.setView([lat, lng], 15);
                setMapLocation(lat, lng);
                document.getElementById('address').value = result.display_name;
            } else {
                alert('Location not found. Please try a different search term.');
            }
        })
        .catch(error => {
            console.error('Geocoding failed:', error);
            alert('Search failed. Please try again.');
        });
}

function goToLocation(lat, lng, name) {
    if (map) {
        map.setView([lat, lng], 13);
        setMapLocation(lat, lng);
        document.getElementById('location_search').value = name;
    }
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            if (map) {
                map.setView([lat, lng], 15);
                setMapLocation(lat, lng);
            }
        }, function(error) {
            alert('Unable to get your location. Please allow location access or search manually.');
        });
    } else {
        alert('Geolocation is not supported by this browser.');
    }
}

function updateMapFromCoordinates() {
    const lat = parseFloat(document.getElementById('latitude').value);
    const lng = parseFloat(document.getElementById('longitude').value);

    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        if (map) {
            map.setView([lat, lng], 15);

            // Remove existing marker
            if (marker) {
                map.removeLayer(marker);
            }

            // Add new marker
            marker = L.marker([lat, lng], {
                draggable: true
            }).addTo(map);

            // Add drag listener to marker
            marker.on('dragend', function(e) {
                const position = e.target.getLatLng();
                document.getElementById('latitude').value = position.lat.toFixed(8);
                document.getElementById('longitude').value = position.lng.toFixed(8);
                reverseGeocode(position.lat, position.lng);
            });

            // Reverse geocode to get address
            reverseGeocode(lat, lng);
        }
    }
}

function updateCommission(shopId, currentCommission) {
    document.getElementById('commission_shop_id').value = shopId;
    document.getElementById('commission_percentage').value = currentCommission;
    new bootstrap.Modal(document.getElementById('commissionModal')).show();
}

function createShopOwner(shopId, shopName) {
    document.getElementById('shop_owner_shop_id').value = shopId;
    document.getElementById('shop_owner_shop_name').textContent = shopName;

    // Generate suggested username from shop name
    const suggestedUsername = shopName.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 15) + '_owner';
    document.getElementById('shop_owner_username').value = suggestedUsername;

    // Clear password fields
    document.getElementById('shop_owner_password').value = '';
    document.getElementById('shop_owner_password_confirm').value = '';

    new bootstrap.Modal(document.getElementById('createShopOwnerModal')).show();
}

// Password confirmation validation
document.getElementById('shop_owner_password_confirm').addEventListener('input', function() {
    const password = document.getElementById('shop_owner_password').value;
    const confirmPassword = this.value;
    const submitBtn = document.getElementById('createShopOwnerBtn');

    if (password !== confirmPassword) {
        this.classList.add('is-invalid');
        submitBtn.disabled = true;
    } else {
        this.classList.remove('is-invalid');
        submitBtn.disabled = false;
    }
});

document.getElementById('shop_owner_password').addEventListener('input', function() {
    const confirmField = document.getElementById('shop_owner_password_confirm');
    if (confirmField.value) {
        confirmField.dispatchEvent(new Event('input'));
    }
});
</script>

<?php include 'includes/footer.php'; ?>
